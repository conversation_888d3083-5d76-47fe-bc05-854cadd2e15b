using Shared.GraphQL.Models;

namespace GraphQLApi.Services;

public interface ITrainingService
{
    IQueryable<Training> GetTrainingsQueryable();
    Task<IEnumerable<Training>> GetAllTrainingsAsync();
    Task<Training?> GetTrainingByIdAsync(int id);
    Task<Training> CreateTrainingAsync(Training training);
    Task<Training?> UpdateTrainingAsync(int id, Training training);
    Task<bool> DeleteTrainingAsync(int id);
}
