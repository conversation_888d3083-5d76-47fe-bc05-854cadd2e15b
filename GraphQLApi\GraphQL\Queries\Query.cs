﻿using GraphQLApi.Services;
using GraphQLApi.Data;
using Shared.GraphQL.Models;
using Task = Shared.GraphQL.Models.Task;
using HotChocolate.Data;

namespace GraphQLApi.GraphQL.Queries
{
    public class Query
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly ITrainingStatusService _trainingStatusService;
        private readonly ITaskService _taskService;
        private readonly IEquipmentService _equipmentService;

        public Query(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService,
            ITrainingStatusService trainingStatusService,
            ITaskService taskService,
            IEquipmentService equipmentService)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _trainingStatusService = trainingStatusService;
            _taskService = taskService;
            _equipmentService = equipmentService;
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Worker> GetAllWorkers([Service] AppDbContext context)
        {
            return context.Workers;
        }

        [UseProjection]
        public IQueryable<Worker> GetWorkerById(int id, [Service] AppDbContext context)
        {
            return context.Workers.Where(w => w.Id == id);
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Training> GetAllTrainings([Service] AppDbContext context)
        {
            return context.Trainings;
        }

        [UseProjection]
        public IQueryable<Training> GetTrainingById(int id, [Service] AppDbContext context)
        {
            return context.Trainings.Where(t => t.Id == id);
        }

        public async Task<IEnumerable<Trade>> GetAllTrades()
        {
            return await _tradeService.GetAllTradesAsync();
        }

        public async Task<Trade?> GetTradeById(int id)
        {
            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<IEnumerable<Skill>> GetAllSkills()
        {
            return await _skillService.GetAllSkillsAsync();
        }

        public async Task<Skill?> GetSkillById(int id)
        {
            return await _skillService.GetSkillByIdAsync(id);
        }

        // Training History Queries
        public async Task<IEnumerable<WorkerTrainingHistory>> GetWorkerTrainingHistory(int workerId)
        {
            return await _trainingStatusService.GetWorkerTrainingHistoryAsync(workerId);
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiringTrainings(int daysAhead = 30)
        {
            return await _trainingStatusService.GetExpiringTrainingsAsync(daysAhead);
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiredTrainings()
        {
            return await _trainingStatusService.GetExpiredTrainingsAsync();
        }

        // Task Queries
        [UseProjection]
        [UseFiltering]
        public IQueryable<Task> GetAllTasks([Service] AppDbContext context)
        {
            return context.Tasks;
        }

        [UseProjection]
        public IQueryable<Task> GetTaskById(int id, [Service] AppDbContext context)
        {
            return context.Tasks.Where(t => t.Id == id);
        }

        public async Task<IEnumerable<Task>> GetTasksByWorkerId(int workerId)
        {
            return await _taskService.GetTasksByWorkerIdAsync(workerId);
        }

        public async Task<IEnumerable<Task>> GetTasksByChiefEngineerId(int chiefEngineerId)
        {
            return await _taskService.GetTasksByChiefEngineerIdAsync(chiefEngineerId);
        }

        public async Task<IEnumerable<Task>> GetTasksByStatus(Shared.Enums.TaskStatus status)
        {
            return await _taskService.GetTasksByStatusAsync(status);
        }

        public async Task<IEnumerable<Task>> GetTasksByPriority(Shared.Enums.TaskPriority priority)
        {
            return await _taskService.GetTasksByPriorityAsync(priority);
        }

        // Equipment Queries
        public async Task<IEnumerable<Equipment>> GetAllEquipment()
        {
            return await _equipmentService.GetAllEquipmentAsync();
        }

        public async Task<Equipment?> GetEquipmentById(int id)
        {
            return await _equipmentService.GetEquipmentByIdAsync(id);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByStatus(string status)
        {
            return await _equipmentService.GetEquipmentByStatusAsync(status);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByCategory(string category)
        {
            return await _equipmentService.GetEquipmentByCategoryAsync(category);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByLocation(string location)
        {
            return await _equipmentService.GetEquipmentByLocationAsync(location);
        }
    }
}
