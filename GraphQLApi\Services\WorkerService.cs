using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.Services
{
    public class WorkerService : IWorkerService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IPhotoService _photoService;
        private readonly ILogger<WorkerService> _logger;

        public WorkerService(
            IDbContextFactory<AppDbContext> contextFactory,
            IPhotoService photoService,
            ILogger<WorkerService> logger)
        {
            _contextFactory = contextFactory;
            _photoService = photoService;
            _logger = logger;
        }

        public IQueryable<Worker> GetWorkersQueryable()
        {
            var context = _contextFactory.CreateDbContext();
            return context.Workers
                .Include(w => w.Trainings)
                .Include(w => w.Trades)
                .Include(w => w.Skills)
                .Include(w => w.TrainingHistory)
                    .ThenInclude(th => th.Training)
                .Include(w => w.Incidents)
                .AsSplitQuery(); // Use split queries for better performance with multiple includes
        }

        public async Task<IEnumerable<Worker>> GetAllWorkersAsync(bool includeRelated = false)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            IQueryable<Worker> query = context.Workers;

            if (includeRelated)
            {
                query = query
                    .Include(w => w.Trainings)
                    .Include(w => w.Trades)
                    .Include(w => w.Skills);
            }

            return await query.ToListAsync();
        }

        public async Task<Worker?> GetWorkerByIdAsync(int id, bool includeTrainings = false, bool includeTrades = false, bool includeSkills = false)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.Workers.AsQueryable();

            if (includeTrainings)
                query = query.Include(w => w.Trainings);
            if (includeTrades)
                query = query.Include(w => w.Trades);
            if (includeSkills)
                query = query.Include(w => w.Skills);

            return await query.FirstOrDefaultAsync(w => w.Id == id);
        }

        public async Task<Worker> CreateWorkerAsync(Worker worker)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Check if worker with same National ID already exists
            var existingWorker = await context.Workers
                .FirstOrDefaultAsync(w => w.NationalId == worker.NationalId);

            if (existingWorker != null)
            {
                throw new GraphQLException(new Error(
                    "Validation",
                    $"A worker with National ID '{worker.NationalId}' already exists.")
                );
            }

            context.Workers.Add(worker);
            await context.SaveChangesAsync();
            return worker;
        }

        public async Task<Worker?> UpdateWorkerAsync(
            int id,
            Worker updatedWorker,
            bool includeTrainings = false,
            bool includeTrades = false,
            bool includeSkills = false)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.Workers.AsQueryable();

            if (includeTrainings)
                query = query.Include(w => w.Trainings);
            if (includeTrades)
                query = query.Include(w => w.Trades);
            if (includeSkills)
                query = query.Include(w => w.Skills);

            var worker = await query.FirstOrDefaultAsync(w => w.Id == id);

            if (worker == null)
                return null;

            // Update properties
            worker.Name = updatedWorker.Name;
            worker.Company = updatedWorker.Company;
            worker.DateOfBirth = updatedWorker.DateOfBirth;
            worker.ManHours = updatedWorker.ManHours;
            worker.Rating = updatedWorker.Rating;
            worker.Gender = updatedWorker.Gender;
            worker.PhoneNumber = updatedWorker.PhoneNumber;
            worker.Email = updatedWorker.Email;
            worker.InductionDate = updatedWorker.InductionDate;
            worker.MedicalCheckDate = updatedWorker.MedicalCheckDate;
            worker.MpesaNumber = updatedWorker.MpesaNumber;

            await context.SaveChangesAsync();
            return worker;
        }

        public async Task<bool> DeleteWorkerAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var worker = await context.Workers.FindAsync(id);
            if (worker == null)
                return false;

            try
            {
                if (!string.IsNullOrEmpty(worker.PhotoUrl))
                {
                    await _photoService.DeletePhotoAsync(worker.PhotoUrl);
                }

                context.Workers.Remove(worker);
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting worker with ID {WorkerId}", id);
                throw;
            }
        }

        public async Task<string> UploadWorkerPhotoAsync(int workerId, Stream photoStream, bool useHikvision = false)
        {
            var worker = await _contextFactory.CreateDbContextAsync();
            var workerEntity = await worker.Workers.FindAsync(workerId);
            if (workerEntity == null)
                throw new ArgumentException("Worker not found", nameof(workerId));

            try
            {
                var fileName = $"worker_{workerId}.jpg";
                var storageType = useHikvision ? PhotoStorageType.Hikvision : PhotoStorageType.Local;

                // Delete existing photo if any
                if (!string.IsNullOrEmpty(workerEntity.PhotoUrl))
                {
                    await _photoService.DeletePhotoAsync(workerEntity.PhotoUrl);
                }

                var photoUrl = await _photoService.UploadPhotoAsync(photoStream, fileName, storageType);
                workerEntity.PhotoUrl = photoUrl;
                await worker.SaveChangesAsync();

                return photoUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo for worker {WorkerId}", workerId);
                throw;
            }
        }

        public async Task<string> TransferWorkerPhotoToHikvisionAsync(int workerId)
        {
            var worker = await _contextFactory.CreateDbContextAsync();
            var workerEntity = await worker.Workers.FindAsync(workerId);
            if (workerEntity == null)
                throw new ArgumentException("Worker not found", nameof(workerId));

            if (string.IsNullOrEmpty(workerEntity.PhotoUrl))
                throw new InvalidOperationException("Worker has no photo to transfer");

            try
            {
                var hikvisionUrl = await _photoService.TransferPhotoToHikvisionAsync(workerEntity.PhotoUrl, workerId.ToString());
                workerEntity.PhotoUrl = hikvisionUrl;
                await worker.SaveChangesAsync();

                return hikvisionUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transferring photo to Hikvision for worker {WorkerId}", workerId);
                throw;
            }
        }
    }
}