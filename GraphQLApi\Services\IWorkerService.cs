using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IWorkerService
    {
        IQueryable<Worker> GetWorkersQueryable();
        Task<IEnumerable<Worker>> GetAllWorkersAsync(bool includeRelated = false);
        Task<Worker?> GetWorkerByIdAsync(int id, bool includeTrainings = false, bool includeTrades = false, bool includeSkills = false);
        Task<Worker> CreateWorkerAsync(Worker worker);
        Task<Worker?> UpdateWorkerAsync(int id, Worker worker, bool includeTrainings = false, bool includeTrades = false, bool includeSkills = false);
        Task<bool> DeleteWorkerAsync(int id);
        //Task<string> UploadWorkerPhotoAsync(int workerId, Stream photoStream);
    }
} 